﻿using Infodice.WE.Common.Enums;

namespace Infodice.WE.Common.DTOs
{
    public class PotentialVotersDTO
    {
        #region common properties

        public int Id { get; set; }

        public long NationalId { get; set; }

        /// <summary>
        /// Gender
        /// </summary>
        public Gender Gender { get; set; }

        /// <summary>
        /// Phone
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// PK: Polling Station
        /// CA: PollLocation
        /// </summary>
        public string PS { get; set; }

        public string PSCode { get; set; }

        /// <summary>
        /// PK: Block Code
        /// CA: PollDiv
        /// </summary>
        public string BlockCode { get; set; }

        /// <summary>
        /// BlockCodeId
        /// </summary>
        public int BlockCodeId { get; set; }

        /// <summary>
        /// PK: Electoral Area 
        /// </summary>
        public string Area { get; set; }
        public string Name { get; set; }

        /// <summary>
        /// PK: NA
        /// CA: Riding
        /// </summary>
        public string NA { get; set; }


        #endregion


        #region PK
        public short SNo { get; set; }

        public string GNo { get; set; }

        public byte[] NameBytes { get; set; }

        public byte[] FNameBytes { get; set; }

        public byte Age { get; set; }

        public byte[] AddressBytes { get; set; }

        /// <summary>
        /// Provincial Constituency
        /// </summary>
        public string PA { get; set; }

        public int PartyId { get; set; }

        public PartyAffiliation PAFL { get; set; }


        /// <summary>
        /// Timestamp in millis when last records was updated
        /// </summary>
        public long TS { get; set; }

        /// <summary>
        /// Row version number
        /// </summary>
        public byte RV { get; set; }

        /// <summary>
        /// Updated by
        /// </summary>
        public string UpdatedBy { get; set; }

        //public Color Color { get; set; }

        #endregion

        public int? PollingStationId { get; set; }

        public int? NAId { get; set; }

        public int? PAId { get; set; }
    }
}
