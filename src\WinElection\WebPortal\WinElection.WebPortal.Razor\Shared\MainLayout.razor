@using CmsPortal.Client.Shared.Shared;
@using Microsoft.AspNetCore.Authorization
@using Newtonsoft.Json
@using WinElection.WebPortal.Razor.Shared.Icons;
@using Microsoft.AspNetCore.Components.Authorization;
@inherits MainLayoutShared
@attribute [Authorize]
@{
    var _params = $"{EventId}/{NaId}/{PaId}/{UcId}";
    var voterDashboard = $"/we/dashboards/voterdashboard/{_params}";
    var teamsDashboard = $"/we/dashboards/teamsdashboard/{_params}";

    var naConstituency = $"/we/Constituency/nas/{_params}";
    var paConstituency = $"/we/Constituency/pas/{_params}";
    var unionCouncils = $"/we/Constituency/ucs/{_params}";
    var wards = $"/we/Constituency/wards/{_params}";
    var blockCodes = $"/we/Constituency/blockCodes/{_params}";
    var pollingStations = $"/we/Constituency/pollingstations/{_params}";

    var appUsers = $"/we/home/<USER>/{_params}";
    var pinCode = $"/we/home/<USER>/{_params}";
    var voteSearch = $"/we/home/<USER>/{_params}";
    var liveSearches = $"/we/home/<USER>/{_params}";

    var markingSummary = $"/we/reports/markingsummary/{_params}";
    var markingReport = $"/we/reports/markingreport/{_params}";

    var level1Coordinators = $"/we/coordinators/level1-coordinators/{_params}";
    var level2Coordinators = $"/we/coordinators/level2-coordinators/{_params}";
    var campaingTeams = $"/we/coordinators/campaing-teams/{_params}";

    var elections = $"/platform/admin/elections2/{_params}";
    // var pollingSchemes = $"/we/ElectoralTiers/pollingschemes/{_params}";
    var scannedVoterlists = $"/we/ElectoralTiers/scannedvoterlists/{_params}";
    // var delimitations = $"/we/ElectoralTiers/delimitations/{_params}";

    var masterData = $"/we/sub-admin/master-data/{_params}";
    var voterData = $"/we/sub-admin/voter-data/{_params}";


    var viewpswiseresult = $"/cms/rms/pollingstationresults/{_params}";
    var contestants = $"/cms/admin/contestants/{_params}";
    var webusers = $"/cms/users/webusers/{_params}";
    var delimitations = $"/cms/admin/delimitations/{_params}";
    var pollingSchemes = $"/cms/admin/pollingschemes/{_params}";
    var scannedData = $"/cms/admin/scanneddata/{_params}";

    var form45scans = $"/rms/scans/form45/{_params}";



}
<style>
    .mud-table-row {
        border-bottom: 1px gainsboro solid;
    }
</style>
<AuthorizeView>
    <Authorized>
        <CascadingValue Value="@context.User">
            <CascadingValue Value="this">

                <div class="antialiased" x-data="{ menu: (window.innerWidth < 768)? false : true, table: true,
        repositionTabMarker(el){
          this.$refs.tabMarker.style.width=el.offsetWidth + 'px';
          this.$refs.tabMarker.style.height=el.offsetHeight + 'px';
          this.$refs.tabMarker.style.left=el.offsetLeft + 'px';
      }
    }" x-cloak x-tabs x-init="repositionTabMarker($el.firstElementChild);">


                    <!-- Sidebar -->

                    <aside :class="['fixed top-0 left-0 z-[70] h-screen transition-transform bg-white border-r border-gray-200 dark:bg-gray-800 dark:border-gray-700',
                                 menu ? 'w-4/5 md:w-64 translate-x-0' : 'md:w-fit -translate-x-full md:translate-x-0']" aria-label="Sidenav" id="drawer-navigation">

                        <div class="h-full px-4 py-5 overflow-y-auto bg-white dark:bg-gray-800">
                            <div class="flex items-center justify-between mb-8">
                                <a href="./index.html" class="flex items-center justify-between">
                                    <MenuIcon />
                                </a>
                                <button x-on:click="menu = false">
                                    <svg aria-hidden="true" class="w-6 h-6 text-gray-600 md:hidden" fill="currentColor" viewBox="0 0 20 20"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd"
                                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="mb-3 text-sm font-bold text-slate-500" :class="menu ? 'block' : 'md:hidden'">DASHBOARDS</div>

                            <ul class="flex flex-col gap-3 mb-8">
                                <AuthorizeView Roles="ADMIN, CMSMANAGER,S-CMSMANAGER,Tele-Canavesser " Context="_local">
                                    <MenuItem Caption="Voter Dashboard" Path="@voterDashboard" IconComponent="typeof(VoterDashboardIcon)" />
                                </AuthorizeView>

                                <AuthorizeView Roles="ADMIN, CMSMANAGER,S-CMSMANAGER" Context="_local">
                                    @*  <MenuItem Caption="Team Dashboard" Path="@teamsDashboard" IconComponent="typeof(TeamDashboardIcon)" /> *@
                                    <MenuItem Caption="Live Searches" Path="@liveSearches" IconComponent="typeof(LivesearchesIcon)" />
                                </AuthorizeView>

                                <MenuGroup Caption="Electoral Units" IconComponent="typeof(ElectoralUnitsIcon)" Roles="ADMIN, CMSMANAGER,S-CMSMANAGER">
                                    @*  <SubMenuItem Caption="Districts" Path="/cms/ElectoralTiers/districts/77560/0/0/0" />
                                    <SubMenuItem Caption="Tehsils" Path="/cms/ElectoralTiers/tehsils/77560/0/0/0" />
                                    <SubMenuItem Caption="National Seats" Path="@naConstituency" IsCompleted="false" />
                                    <SubMenuItem Caption="Provincial Seats" Path="@paConstituency" />

                                    <SubMenuItem Caption="Wards" Path="@wards" />
                                    <SubMenuItem Caption="UCs/VCs" Path="@unionCouncils" /> *@
                                    <SubMenuItem Caption="Block Codes" Path="@blockCodes" />
                                    <SubMenuItem Caption="Polling Stations" Path="@pollingStations" />
                                </MenuGroup>
                                <AuthorizeView Roles="ADMIN, CMSMANAGER,S-CMSMANAGER" Context="_local">
                                    <MenuItem Caption="App Users" Path="@appUsers" IconComponent="typeof(MobileAccessIcon)" />
                                    <MenuItem Caption="Pin Code" Path="@pinCode" IconComponent="typeof(PinCodesIcon)" />
                                </AuthorizeView>

                                <AuthorizeView Roles="ADMIN" Context="_local">

                                    <MenuGroup Caption="Result Management" IconComponent="typeof(ElectionResultsIcon)">
                                        <SubMenuItem Caption="View PS Wise Result" Path="@viewpswiseresult" />
                                        <SubMenuItem Caption="Form 45 Scans" Path="@form45scans" />
                                        @*<SubMenuItem Caption="Results Dashboard" Path="/cms/admin/delimitations/77560/0/0/0" />
                                        <SubMenuItem Caption="Result Management" Path="/cms/admin/delimitations/77560/0/0/0" /> *@
                                    </MenuGroup>
                                </AuthorizeView>
                                <AuthorizeView Roles="Tele-Canavesser" Context="_local">
                                    <MenuItem Caption="Form 45 Scans" Path="@form45scans" IconComponent="typeof(ElectionResultsIcon)" />
                                </AuthorizeView>
                                <MenuGroup Caption="Teams" IconComponent="typeof(TeamsIcon)" Roles="ADMIN, CMSMANAGER,S-CMSMANAGER">
                                    @* <MenuItem Caption="PP/NA Coordinators" Path="@level1Coordinators" IconComponent="typeof(VoterDashboardIcon)" />
                                    <MenuItem Caption="UC Coordinators" Path="@level2Coordinators" IconComponent="typeof(VoterDashboardIcon)" /> *@
                                    <SubMenuItem Caption="Polling Locations" Path="@campaingTeams" />
                                </MenuGroup>

                                <MenuGroup Caption="Reports" IconComponent="typeof(ReportsIcon)" Roles="ADMIN, CMSMANAGER,S-CMSMANAGER">
                                    <SubMenuItem Caption="Vote Search" Path="@voteSearch" />
                                    <SubMenuItem Caption="Marking Report" Path="@markingReport" />
                                    <SubMenuItem Caption="Marking Summary" Path="@markingSummary" />

                                    @*
                                    <SubMenuItem Caption="Zero Vote Marked" Path="/cms/reports/ZeroVoteMarkingReport/77560/0/0/0" />
                                    <SubMenuItem Caption="PS Campaign Coordinators" Path="/cms/reports/PollingStationCoordinator/77560/0/0/0" />
                                    <SubMenuItem Caption="Polling Agent" Path="/cms/reports/PollingAgentReport/77560/0/0/0" />
                                    <SubMenuItem Caption="Campaign Members Block Code Wise" Path="/cms/reports/CampaignMemebersBwise/77560/0/0/0" />
                                    <SubMenuItem Caption="Chief Polling Agent" Path="/cms/reports/ChiefPollingAgentReport/77560/0/0/0" />
                                    <SubMenuItem Caption="Families Marking Summary" Path="/cms/reports/IdentifiedFamiliesReport/77560/0/0/0" />
                                    <SubMenuItem Caption="Food Incharge" Path="/cms/reports/foodinchargereport/77560/0/0/0" /> *@

                                </MenuGroup>
                                <MenuGroup Caption="Admin" IconComponent="typeof(Cog)" Roles="ADMIN">
                                    <MenuItem Caption="Elections" Path="@elections" IconComponent="typeof(Bullhorn)" IsCompleted="true" />
                                    <MenuItem Caption="Polling Schemes" Path="@pollingSchemes" IconComponent="typeof(Link)" IsCompleted="true" />
                                    <MenuItem Caption="Scanned Data" Path="@scannedData" IconComponent="typeof(ScannerImage)" IsCompleted="true" />
                                    <MenuItem Caption="Delimitations" Path="@delimitations" IconComponent="typeof(CalendarDay)" IsCompleted="true" />
                                    <MenuItem Caption="Web Users" Path="@webusers" IconComponent="typeof(UserUnlock)" IsCompleted="false" />
                                    @* <MenuItem Caption="Contestants" Path="@contestants" IconComponent="typeof(VoterDashboardIcon)" /> *@
                                </MenuGroup>
                                <MenuGroup Caption="Sub-admin" IconComponent="typeof(Cogs)" Roles="ADMIN,S-CMSMANAGER">
                                    <MenuItem Caption="Master Data" Path="@masterData" IconComponent="typeof(VoterDashboardIcon)" />
                                    <MenuItem Caption="Voter Data" Path="@voterData" IconComponent="typeof(VoterDashboardIcon)" />
                                </MenuGroup>

                            </ul>
                        </div>
                    </aside>

                    <div class="" :class="menu ? 'md:ml-64' : 'md:ml-20'">
                        <nav class="bg-white border-b border-gray-300 px-4 h-16 py-2.5 dark:bg-gray-800 dark:border-gray-700 sticky left-0 right-0 top-0 z-[60]">
                            <div class="flex flex-wrap items-center justify-between">
                                <div class="flex items-center justify-start gap-2">
                                    <button x-on:click="menu = !menu"
                                            aria-controls="drawer-navigation"
                                            class="text-gray-600 rounded-lg cursor-pointer md:p-2 hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 21 14" aria-controls="drawer-navigation">
                                            <path d="M1.58916 1.29843H15.2216" stroke="#455569" stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M1.58984 7.00009H19.5831" stroke="#455569" stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M1.58984 12.7015H12.4548" stroke="#455569" stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <span class="sr-only">Toggle sidebar</span>
                                    </button>
                                    <a class="shrink-0" href="/" :class="menu ? 'md:hidden' : 'md:hidden'">
                                        <svg width="34" height="18" viewBox="0 0 34 18" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M27.5583 0.311157H22.6092L15.7266 17.1888H20.6756L27.5583 0.311157V0.311157Z" fill="#35A853" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M33.9993 0.311157H29.0502L27.4531 4.2275H32.4022L33.9993 0.311157Z" fill="#0194E1" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M26.4076 6.79187L24.8105 10.7082H29.7596L31.3567 6.79187H26.4076Z" fill="#0194E1" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M23.7651 13.2725L22.168 17.1887H27.117L28.7141 13.2725H23.7651Z" fill="#FBBB02" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0.311157H4.94912L11.8318 17.1888H6.88279L0 0.311157Z" fill="#EC4130" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.75977 0.311157H13.7088L20.5916 17.1888H15.6424L8.75977 0.311157Z" fill="#0194E1" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.8357 0.311157H13.8867L7.00391 17.1888H11.953L18.8357 0.311157Z" fill="#35A853" />
                                        </svg>
                                    </a>
                                </div>
                                <div id="headerMainLayout" class="flex items-center md:gap-3 lg:order-2">

                                    <EditForm Model="new object()" Context="ElectionsModel" class="d-flex align-items-center" style="width:250px;">
                                        @* <Select2 ModalId="headerMainLayout" Datasource="@Elections" @bind-Value="@EventId" Id="_globalEventId"></Select2> *@

                                        <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 flex grow w-full md:w-44 pl-2.5 pr-5 py-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                            <option value="">Select an Election</option>
                                            @if (Elections != null)
                                            {
                                                @foreach (var e in Elections)
                                                {
                                                    <option value="@e.Value">@e.Text</option>
                                                }
                                            }
                                        </select>

                                    </EditForm>

                                    <button class="hidden p-2 text-gray-600 rounded-lg cursor-pointer md:flex shrink-0 hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <span class="sr-only">Refresh</span>
                                        <svg aria-expanded="false" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                            <path d="M17.323 8.74997C17.323 8.36337 17.0096 8.04997 16.623 8.04997C16.2364 8.04997 15.923 8.36337 15.923 8.74997H17.323ZM2.94533 11.7427C2.74494 11.4121 2.31448 11.3065 1.98387 11.5069C1.65326 11.7073 1.54769 12.1378 1.74808 12.4684L2.94533 11.7427ZM2.3467 11.4055C1.96011 11.4055 1.6467 11.7189 1.6467 12.1055C1.6467 12.4921 1.96011 12.8055 2.3467 12.8055V11.4055ZM5.76274 12.8055C6.14933 12.8055 6.46274 12.4921 6.46274 12.1055C6.46274 11.7189 6.14933 11.4055 5.76274 11.4055V12.8055ZM3.0467 12.1055C3.0467 11.7189 2.7333 11.4055 2.3467 11.4055C1.96011 11.4055 1.6467 11.7189 1.6467 12.1055H3.0467ZM1.6467 15.8843C1.6467 16.2709 1.96011 16.5843 2.3467 16.5843C2.7333 16.5843 3.0467 16.2709 3.0467 15.8843H1.6467ZM0.807813 8.74997C0.807813 9.13657 1.12121 9.44997 1.50781 9.44997C1.89441 9.44997 2.20781 9.13657 2.20781 8.74997H0.807813ZM16.0225 5.75407C16.2211 6.08574 16.651 6.19357 16.9827 5.99493C17.3143 5.79629 17.4222 5.3664 17.2235 5.03473L16.0225 5.75407ZM15.923 5.3944C15.923 5.781 16.2364 6.0944 16.623 6.0944C17.0096 6.0944 17.323 5.781 17.323 5.3944H15.923ZM17.323 1.61561C17.323 1.22901 17.0096 0.915608 16.623 0.915608C16.2364 0.915608 15.923 1.22901 15.923 1.61561H17.323ZM16.623 6.0944C17.0096 6.0944 17.323 5.781 17.323 5.3944C17.323 5.0078 17.0096 4.6944 16.623 4.6944V6.0944ZM13.2674 4.6944C12.8808 4.6944 12.5674 5.0078 12.5674 5.3944C12.5674 5.781 12.8808 6.0944 13.2674 6.0944V4.6944ZM15.923 8.74997C15.923 12.5352 12.8506 15.6076 9.0654 15.6076V17.0076C13.6238 17.0076 17.323 13.3084 17.323 8.74997H15.923ZM9.0654 15.6076C7.2226 15.6076 5.70398 14.6786 4.61191 13.6881C4.07036 13.1969 3.64923 12.7041 3.36405 12.3345C3.22185 12.1502 3.11452 11.9979 3.04394 11.8937C3.00867 11.8416 2.98266 11.8016 2.96613 11.7757C2.95787 11.7628 2.95198 11.7534 2.9485 11.7478C2.94676 11.745 2.94563 11.7432 2.9451 11.7423C2.94483 11.7419 2.94472 11.7417 2.94476 11.7418C2.94478 11.7418 2.94483 11.7419 2.94493 11.742C2.94498 11.7421 2.94508 11.7423 2.9451 11.7423C2.94521 11.7425 2.94533 11.7427 2.3467 12.1055C1.74808 12.4684 1.74822 12.4686 1.74837 12.4689C1.74843 12.469 1.74859 12.4692 1.74871 12.4694C1.74896 12.4698 1.74926 12.4703 1.74958 12.4708C1.75024 12.4719 1.75106 12.4733 1.75204 12.4749C1.75399 12.478 1.75657 12.4822 1.75977 12.4874C1.76619 12.4977 1.77511 12.5119 1.78652 12.5297C1.80934 12.5654 1.84212 12.6157 1.88464 12.6785C1.96964 12.8041 2.09388 12.9801 2.25564 13.1897C2.57837 13.608 3.0547 14.1657 3.67135 14.725C4.89569 15.8355 6.73641 17.0076 9.0654 17.0076V15.6076ZM2.3467 12.8055H5.76274V11.4055H2.3467V12.8055ZM1.6467 12.1055V15.8843H3.0467V12.1055H1.6467ZM2.20781 8.74997C2.20781 4.96296 5.25181 1.89238 9.0654 1.89238V0.492383C4.47496 0.492383 0.807813 4.19341 0.807813 8.74997H2.20781ZM9.0654 1.89238C11.378 1.89238 13.1142 2.85328 14.2861 3.83092C14.8725 4.32009 15.3123 4.80954 15.6041 5.17474C15.7497 5.35697 15.8575 5.50713 15.9274 5.60928C15.9623 5.66032 15.9878 5.69926 16.0036 5.72409C16.0115 5.7365 16.0171 5.74538 16.0202 5.75046C16.0218 5.753 16.0228 5.7546 16.0231 5.75521C16.0233 5.75552 16.0234 5.75558 16.0233 5.75539C16.0232 5.75529 16.0231 5.75514 16.023 5.75492C16.0229 5.75481 16.0228 5.7546 16.0227 5.75454C16.0226 5.75432 16.0225 5.75407 16.623 5.3944C17.2235 5.03473 17.2234 5.03446 17.2232 5.03417C17.2231 5.03405 17.2229 5.03374 17.2228 5.0335C17.2225 5.03303 17.2222 5.03249 17.2218 5.03189C17.2211 5.03068 17.2202 5.02922 17.2192 5.0275C17.2171 5.02408 17.2144 5.01964 17.211 5.01424C17.2043 5.00342 17.1951 4.9887 17.1834 4.97033C17.1599 4.93361 17.1263 4.88227 17.0826 4.81841C16.9952 4.69076 16.867 4.51265 16.6979 4.30093C16.3603 3.87826 15.8558 3.3172 15.1829 2.75587C13.8363 1.63249 11.7937 0.492383 9.0654 0.492383V1.89238ZM17.323 5.3944V1.61561H15.923V5.3944H17.323ZM16.623 4.6944H13.2674V6.0944H16.623V4.6944Z" fill="#455569" />
                                        </svg>
                                    </button>
                                    <div class="ml-2 shrink-0 md:ml-0" x-menu>
                                        <button type="button"
                                                class="flex items-center gap-1 text-sm rounded-full"
                                                id="user-menu-button" x-menu:button>
                                            <img class="w-8 h-8 rounded-full shrink-0" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/michael-gough.png" alt="user photo" />
                                            <span class="hidden text-xs font-normal text-slate-500 md:block">@context.User.Identity.Name</span>
                                        </button>
                                        <!-- Dropdown menu -->
                                        <div x-menu:items class="absolute right-0 z-50 w-56 my-4 text-base list-none bg-white divide-y divide-gray-100 shadow-sm dark:bg-gray-700 dark:divide-gray-600 rounded-xl"
                                             id="dropdown">
                                            <div class="px-4 py-3">

                                                <span class="block text-sm text-gray-900 truncate dark:text-white">@context.User.Identity.Name</span>
                                            </div>

                                            <ul class="py-1 text-gray-700 dark:text-gray-300" aria-labelledby="dropdown">
                                                <li>
                                                    <a href="javascript:;" @onclick="Logout"
                                                       class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        Sign
                                                        out
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </nav>
                        @if (NotificationService != null && NotificationService.Notifications.Count() > 0)
                        {

                            var notifications = NotificationService.Notifications;
                            <section class="bg-gray-300 dark:bg-gray-900 h-full">
                                <div class="py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
                                    <div class="mx-auto max-w-screen-sm text-center">
                                        <h1 class="mb-4 text-7xl tracking-tight font-extrabold lg:text-9xl text-primary-600 dark:text-primary-500">Error!</h1>
                                        <p class="mb-4 text-3xl tracking-tight font-bold text-gray-900 md:text-4xl dark:text-white">Something's went wrong.</p>
                                        <p class="mb-4 text-lg font-bold text-red-800 dark:text-red-400">
                                            @foreach (var notification in notifications)
                                            {
                                                <div>@notification.Message</div>
                                            }
                                            @{
                                                NotificationService.Notifications.Clear();
                                            }
                                        </p>
                                        <a href="/" class="inline-flex text-white bg-primary-600 hover:bg-primary-800 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-sm text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900 my-4">Back to Homepage</a>
                                    </div>
                                </div>
                            </section>

                        }
                        else
                        {
                            @Body
                        }

                    </div>

                </div>
                <style>
                    .square {
                        border-radius: 0 !important;
                    }

                    /*     .rz-dialog-side-position-right {
                                                                                                                                                width: 400px;
                                                                                                                                            } */

                    .dw-sm {
                        width: 24rem;
                    }

                    .dw-md {
                        width: 30rem;
                    }

                    .dw-lg {
                        width: 40rem;
                    }


                    .dw-full {
                        width: 100%;
                    }

                </style>
                @if (DialogService != null)
                {
                    foreach (var dialog in DialogService.Dialogs)
                    {
                        <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">

                            <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>

                            <div class="z-[150] fixed inset-0 w-screen overflow-y-auto @dialog.DialogContainerClasses">
                                <div class="w-[calc(100%-0.5rem)] @dialog.PositionClasses m-1 flex @dialog.SizeClasses">
                                    <div class="w-full overflow-y-auto overflow-x-hidden rounded-lg bg-white shadow-lg">

                                        <div class="sticky top-0 z-40 flex items-center justify-between rounded-t border-b bg-white p-2 md:p-4">
                                            <h3 class="truncate text-sm font-semibold text-gray-900 md:text-xl">
                                                @dialog.Title
                                            </h3>
                                            <button @onclick='() => CloseMe(dialog)' type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                                                <i class="fa-solid fa-xmark text-lg text-gray-900"></i>
                                                <span class="sr-only">Close modal</span>
                                            </button>

                                        </div>
                                        <div class="w-full">
                                            <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }
                @if (NotificationService != null)
                {
                    @foreach (var notification in NotificationService.Notifications)
                    {
                        if (notification.NotificationType == Framework.Core.UIServices.NotificationType.Error && notification.Message == "Please re-login to continue using system")
                        {
                            NavigationManager?.NavigateTo($"/identity/account/login", true);
                        }
                        else
                        {
                            <div class="bg-red-100 text-red-800">@notification.Message</div>
                        }
                    }
                }
            </CascadingValue>
        </CascadingValue>
    </Authorized>
    <NotAuthorized>
        @{
            NavigationManager?.NavigateTo($"/identity/account/login", true);
        }
        <span class="hidden text-amber-700 border border-amber-400 hover:bg-amber-100 focus:ring-amber-300"></span>
        <span class="hidden text-green-700 border border-green-400 hover:bg-green-100 focus:ring-green-300"></span>
        <span class="hidden text-rose-700 border border-rose-400 hover:bg-rose-700 hover:bg-rose-50 hover:bg-rose-100 focus:ring-4 focus:outline-hidden focus:ring-rose-300"></span>
    </NotAuthorized>
</AuthorizeView>

