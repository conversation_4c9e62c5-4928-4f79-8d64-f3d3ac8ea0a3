﻿using Framework.Core.UIServices;
using Framework.Core;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Platform.Common.Enums;
using System.ComponentModel;
using Webportal.ServiceContracts.Common;
using Webportal.ServiceContracts;
using Microsoft.Extensions.DependencyInjection;
using Platform.Common.Constants;

namespace CmsPortal.Client.Shared.Shared
{

    public partial class MainLayoutShared : FrameworkLayoutBaseComponent, IMainLayout, INotifyPropertyChanged
    {

        #region Global Variables

        private int _electionGroupId;

        public int ElectionGroupId
        {
            get { return _electionGroupId; }
            set
            {
                SetProperty(ref _electionGroupId, value);
            }
        }

        public IEnumerable<SelectListItem> ElectionGroups { get; set; }
        public List<SelectListItem> Elections { get; set; }

        private SelectListItem? _selectedNAs;
        public SelectListItem? SelectedNAs
        {
            get { return _selectedNAs; }
            set
            {
                if (_selectedNAs != value)
                {
                    _selectedNAs = value;
                    NaId = _selectedNAs.IntValue.GetValueOrDefault();
                }
            }
        }


        private SelectListItem? _selectedPAs;
        public SelectListItem? SelectedPAs
        {
            get { return _selectedPAs; }
            set
            {
                if (_selectedPAs != value)
                {
                    _selectedPAs = value;
                    PaId = _selectedPAs.IntValue.GetValueOrDefault();
                }
            }
        }

        private SelectListItem? _selectedUCs;
        public SelectListItem? SelectedUCs
        {
            get { return _selectedUCs; }
            set
            {
                if (_selectedUCs != value)
                {
                    _selectedUCs = value;
                    PaId = _selectedUCs.IntValue.GetValueOrDefault();
                }
            }
        }

        protected IEnumerable<SelectListItem>? NAs { get; set; }

        protected IEnumerable<SelectListItem>? PAs { get; set; }

        protected IEnumerable<SelectListItem>? UCs { get; set; }
         

        private int _eventId;

        public int EventId
        {
            get { return _eventId; }
            set
            {
                if (_eventId != value && value >0)
                {
                    SetProperty(ref _eventId, value);
                }
            }
        }

        private int _naId;

        public int NaId
        {
            get { return _naId; }
            set
            {
                if (_naId != value)
                {
                    SetProperty(ref _naId, value);
                }
            }
        }

        private int _paId;

        public int PaId
        {
            get { return _paId; }
            set
            {
                if (_paId != value)
                {
                    SetProperty(ref _paId, value);
                }
            }
        }

        private int _ucId;

        public int UcId
        {
            get { return _ucId; }
            set
            {
                if (_ucId != value)
                {
                    SetProperty(ref _ucId, value);
                }
            }
        }

        public bool RefreshFlag { get; set; }

        private string? _headerMessage;

        public string? HeaderMessage
        {
            get { return _headerMessage; }
            set
            {
                SetProperty(ref _headerMessage, value);
                StateHasChanged();
            }
        }

        private bool _isTableBusy;
        public bool IsTableBusy
        {
            get
            {
                return _isTableBusy;
            }
            set
            {
                if (_isTableBusy != value)
                {
                    _isTableBusy = value;
                    StateHasChanged();
                }
            }
        }

        public bool HasElectionGroupFilter { get; set; }
        public bool HasElectionFilter { get; set; }
        public bool HasNaFilter { get; set; }

        public bool HasPaFilter { get; set; }

        public bool HasUcFilter { get; set; }
        public bool ShowPhoneBankPhnos { get; set; }
        public ElectionInfoFormViewModel ElectionInfo { get; set; } = new ElectionInfoFormViewModel();

        string? previousUrl = null;

        #endregion
          
        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();

            if (User == null)
            {
                NavigationManager?.NavigateTo("identity/account/login", true);
                return;
            }

            PropertyChanged += async (p, q) =>
            {

                if (q.PropertyName == nameof(EventId)
                        || q.PropertyName == nameof(NaId)
                        || q.PropertyName == nameof(PaId)
                        || q.PropertyName == nameof(UcId))   
                {
                    var _service = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<ISelectListService>();

                    ElectionInfo = await _service.GetelectionInfo(EventId);
                    var url = new Uri(NavigationManager.Uri);
                    if (url.Segments.Length < 4 || url.ToString().ToLower().Contains("login"))
                        return;

                    var newUrl = $"/{url.Segments.Skip(1).First()}{url.Segments.Skip(2).First()}{url.Segments.Skip(3).First()}{EventId}/{NaId}/{PaId}/{UcId}";
                    foreach (var segment in url.Segments.Skip(8))
                    {
                        newUrl += "/" + segment.Trim('/');
                    }

                    if (newUrl == previousUrl)
                        return;

                    previousUrl = newUrl;
                    if (!NavigationManager.Uri.EndsWith(newUrl))
                    {
                        try { NavigationManager.NavigateTo(newUrl); } catch { }
                    }

                    if (q.PropertyName == nameof(EventId))
                    {
                        NAs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.NA);
                        PAs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.PA);
                        UCs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.UnionCouncil);
                    }
                    else
                    {
                        if (NaId > 0 && q.PropertyName == nameof(NaId))
                        {
                            PAs = await _service.GetChildLocations(EventId, ElectoralTierLevel.PA, NaId);
                            UCs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.UnionCouncil);
                        }
                        if (PaId > 0 && q.PropertyName == nameof(PaId))
                            UCs = await _service.GetChildLocations(EventId, ElectoralTierLevel.UnionCouncil, PaId);
                    }
                }
            };

            try
            {
                var _service = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<ISelectListService>();
                if (Elections == null || Elections.Count() == 0)
                {
                    if (User != null && User.IsInRole(Constants.Roles.ADMIN))
                    {
                        var list = await _service.GetAllElections();
                        if (list != null && list.Count > 0)
                        {
                            Elections = new List<SelectListItem>();
                            foreach (var item in list.Where(x => x.EventType != EventType.ElectionGroup))
                            {
                                Elections.Add(new SelectListItem { IntValue = item.Id, Text = item.Description });
                            }
                            Elections.Insert(0, new(0, "All"));
                        }


                        ElectionInfo = await _service.GetelectionInfo(EventId);

                        if (ElectionInfo != null)
                            ShowPhoneBankPhnos = ElectionInfo.ShowPhoneBankPhnos;
                    }
                    else
                    {
                        Elections = await _service.GetElectionsByUserId("fake");

                        EventId = Elections.First().IntValue.GetValueOrDefault();
                        NAs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.NA);
                        PAs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.PA);
                        UCs = await _service.GetLocationsByEventIdAndElectoralTier(EventId, ElectoralTierLevel.UnionCouncil);
                        ElectionInfo = await _service.GetelectionInfo(EventId);
                        if (ElectionInfo != null)
                            ShowPhoneBankPhnos = ElectionInfo.ShowPhoneBankPhnos;

                    }

                    ElectionGroups = await _service.GetElectionGroups();
                     
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("invalid_token"))
                {
                    NavigationManager.NavigateTo("/identity/account/login");
                }
                NotificationService?.ShowError(ex);
            }


        }
         
        public async Task Logout()
        {
            //if (AuthenticationStateProvider is CustomAuthStateProvider)
            //{
            //    var customAuthStateProider = AuthenticationStateProvider as CustomAuthStateProvider;
            //    if (customAuthStateProider != null)
            //    {
            //        await customAuthStateProider.Logout();
            //        NavigationManager.NavigateTo($"/identity/account/login", true);
            //    }
            //}
        }
        public async Task ShowModal(string id)
        {
            await Task.Delay(10);
            await JsRuntime.InvokeVoidAsync("openDialog", id);

        }
         
        public void CloseMe(ModalDialogConfig dialogConfig)
        {
            // Pure C# Blazor dialog close mechanism
            DialogService!.Dialogs.Remove(dialogConfig);
            StateHasChanged();
        }

        private void Dialogs_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        public Task<IEnumerable<SelectListItem>> SearchableDataSource(string searchText, IEnumerable<SelectListItem> dataSource)
        {
            throw new NotImplementedException();
        }
    }
}
