﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Infodice.WE.Common.Maui.Dtos.Survey
{
    public class MetaSurveyQuestionChoiceDto
    {
        public int Id { get; set; }

        public int MetaQuestionId { get; set; }

        // [StringLength(maximumLength: 450)]
        public string Text { get; set; }

        public bool IsOther { get; set; }

        public byte SortOrder { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}
