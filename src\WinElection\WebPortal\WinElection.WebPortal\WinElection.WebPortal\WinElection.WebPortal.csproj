<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-WinElection.WebPortal-51e8ff1e-45fb-4942-9101-b01f772c3b4f</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="package-lock.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Platform\WebPortal\CmsPortal.Server.DataServices\CmsPortal.Server.DataServices.csproj" />
    <ProjectReference Include="..\..\WinElection.WebPortal.ServiceDefaults\WinElection.WebPortal.ServiceDefaults.csproj" />
    <ProjectReference Include="..\WinElection.WebPortal.Client\WinElection.WebPortal.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
    <PackageReference Include="NetTopologySuite" Version="2.6.0" />
    <PackageReference Include="NetTopologySuite.IO.SqlServerBytes" Version="2.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="package.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="tailwind.config.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="postcss.config.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Styles\" />
  </ItemGroup>

  <!-- Tailwind CSS Build Target -->
  <Target Name="BuildTailwindCSS" BeforeTargets="Build" Condition="'$(Configuration)' == 'Debug'">
    <!--<Exec Command="npm install" ContinueOnError="true" />-->
    <Exec Command="npm run build-css"   />
  </Target>

  <Target Name="BuildTailwindCSSProduction" BeforeTargets="Build" Condition="'$(Configuration)' == 'Release'">
    <!--<Exec Command="npm install" ContinueOnError="true" />-->
    <Exec Command="npm run build-css-prod"  />
  </Target>

</Project>
