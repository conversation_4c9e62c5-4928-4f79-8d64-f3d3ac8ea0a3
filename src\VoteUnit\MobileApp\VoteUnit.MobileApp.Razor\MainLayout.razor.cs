using Framework.Core.UIServices;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace VoteUnit.MobileApp.Razor
{
    public partial class MainLayout
    {
        bool _drawerOpen = true;

        [Inject]
        public NavigationManager NavigationManager { get; set; }
        void DrawerToggle()
        {
            _drawerOpen = !_drawerOpen;
        }
        MudTheme MyCustomTheme = new MudTheme()
        {
            Palette = new Palette()
            {
                Primary = new MudBlazor.Utilities.MudColor("#245C0E"),
                Secondary = new MudBlazor.Utilities.MudColor("#DEAF09"),
                AppbarBackground = new MudBlazor.Utilities.MudColor("#245C0E")
            },

            LayoutProperties = new LayoutProperties()
            {
                DrawerWidthLeft = "260px",
                DrawerWidthRight = "300px"
            }
        };

        public MainLayout()
        {
            PubSub.Hub.Default.Subscribe<string>(x =>
            {
                if ("close_dawer" == x)
                {
                    _drawerOpen = false;
                }
            });
        }
        public void ShowModal(string id)
        {
            // Pure C# Blazor approach - dialog visibility is controlled by CSS classes
            // No JavaScript needed as dialogs are rendered based on DialogService.Dialogs collection
            StateHasChanged();
        }

        public void CloseMe(ModalDialogConfig dialogConfig)
        {
            // Pure C# Blazor dialog close mechanism
            DialogService!.Dialogs.Remove(dialogConfig);
            StateHasChanged();
        }

        private void Dialogs_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }
    }
}