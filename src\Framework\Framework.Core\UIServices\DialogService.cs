﻿using System.Collections.ObjectModel;
using Microsoft.AspNetCore.Components;

namespace Framework.Core.UIServices
{
    public enum DialogType
    {
        CenterMud = 0,
        CenterMedium = 1,
        CenterWide = 2,
        RightMedium = 3,
        RightWide = 4,
        CenterFull = 5
    }
    public class DialogService : Framework.Core.IDialogService
    {
        public ObservableCollection<ModalDialogConfig> Dialogs { get; set; }

        public event Action<dynamic> OnClose;

        public DialogService()
        {
            Dialogs = new ObservableCollection<ModalDialogConfig>();
        }

        public void ShowDialogAsync(ModalDialogConfig dialogConfig)
        {
            Dialogs.Add(dialogConfig);
        }

        public void OpenAsync<T, TKey>(string title, TKey id, params KeyValuePair<string, object>[] parameters) where T : ComponentBase
        {
            var dialogConfig = new ModalDialogConfig
            {
                Title = title,
                Component = typeof(T)
            };

            // Add default parameters
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    dialogConfig.AddParameter(param.Key, param.Value);
                }
            }

            // Add ID parameter
            dialogConfig.AddParameter("Id", id);

            Dialogs.Add(dialogConfig);
        }

        public void OpenAsync<T, TKey>(string title, TKey id, int width, params KeyValuePair<string, object>[] parameters) where T : ComponentBase
        {
            var dialogConfig = new ModalDialogConfig
            {
                Title = title,
                Component = typeof(T)
            };

            // Add default parameters
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    dialogConfig.AddParameter(param.Key, param.Value);
                }
            }

            // Add ID parameter
            dialogConfig.AddParameter("Id", id);

            // Add width parameter if specified
            if (width > 0)
            {
                dialogConfig.AddParameter("Width", width);
            }

            Dialogs.Add(dialogConfig);
        }

        public Task<dynamic> OpenSideAsync<T>(string title, Dictionary<string, object> parameters = null) where T : ComponentBase
        {
            // Validate parameters - handle null gracefully
            var safeParameters = parameters ?? new Dictionary<string, object>();

            var dialogConfig = new ModalDialogConfig
            {
                Title = title,
                Component = typeof(T)
            };

            // Add all parameters safely
            foreach (var param in safeParameters)
            {
                if (param.Key != null) // Additional null safety
                {
                    dialogConfig.AddParameter(param.Key, param.Value);
                }
            }

            Dialogs.Add(dialogConfig);

            // Return a completed task with the dialog config
            return Task.FromResult<dynamic>(dialogConfig);
        }

        public void Close(dynamic dialogResult)
        {
            // Find and remove the dialog
            if (dialogResult is ModalDialogConfig config)
            {
                Dialogs.Remove(config);
            }
            else if (dialogResult is string dialogId)
            {
                var dialog = Dialogs.FirstOrDefault(d => d.Id == dialogId);
                if (dialog != null)
                {
                    Dialogs.Remove(dialog);
                }
            }

            // Trigger the OnClose event
            OnClose?.Invoke(dialogResult);
        }
    }
    public class ModalDialogConfig
    {

        public ModalDialogConfig()
        {
            Id = $"Dialog_{Guid.NewGuid():N}";
            Parameters = new Dictionary<string, object>();
        }

        public void AddParameter(string key, object value)
        {
            // Ensure Parameters is never null before adding
            if (Parameters == null)
                Parameters = new Dictionary<string, object>();

            Parameters.Add(key, value);
        }

        public string Id { get; set; }

        public string Title { get; set; }

        public string SizeClasses { get; set; }

        public string PositionClasses { get; set; }

        public string DialogContainerClasses { get; set; }

        public bool ShowCrossIcon { get; set; } = true;

        public Type Component { get; set; }

        public DialogType Direction { get; set; } = DialogType.CenterMedium;

        private IDictionary<string, object> _parameters;
        public IDictionary<string, object> Parameters
        {
            get
            {
                // Ensure Parameters is never null when accessed
                if (_parameters == null)
                    _parameters = new Dictionary<string, object>();
                return _parameters;
            }
            set
            {
                // Ensure we never set Parameters to null
                _parameters = value ?? new Dictionary<string, object>();
            }
        }
    }



    public class NotificationService
    {
        public ObservableCollection<Notification> Notifications { get; set; }

        public NotificationService()
        {
            Notifications = new ObservableCollection<Notification>();
        }

        public void ShowError(Exception ex)
        {
            Notifications.Add(new Notification()
            {
                Message = $"{ex.Message} {ex.InnerException?.Message}",
                NotificationType = NotificationType.Error,
            });

        }


        public void ShowError(string message)
        {
            Notifications.Add(new Notification()
            {
                Message = message,
                NotificationType = NotificationType.Error,
            });
        }
    }

    public class Notification
    {
        public string? Message { get; set; }
        public NotificationType NotificationType { get; set; }
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,

    }
}

