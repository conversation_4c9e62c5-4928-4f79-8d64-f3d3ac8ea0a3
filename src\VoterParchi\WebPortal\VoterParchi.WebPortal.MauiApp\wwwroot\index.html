
<!DOCTYPE html>
<html data-bs-theme="light" style="--blazor-load-percentage: 100%; --blazor-load-percentage-text: 100;">
<!--begin::Head-->

<head>
    <base href="/" />
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Katibeh&display=swap" rel="stylesheet">
    <style>
        .urdu {
            font-family: 'Katibeh', cursive;
        }
    </style>
    <link rel="shortcut icon" href="/assets/media/logos/favicon.ico">
    <!--begin::Fonts-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700">
    <!--end::Fonts-->
    <!--begin::Global Stylesheets(mandatory)-->
    <link rel="stylesheet" type="text/css" href="/assets/plugins/global/plugins.bundle.css">
    <link rel="stylesheet" type="text/css" href="/assets/css/style.bundle.css">


    <script>
        var defaultThemeMode = "light";
        var themeMode;

        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }

            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }

            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>

    <!--begin::Global Javascript(mandatory)-->
    <script src="/assets/plugins/global/plugins.bundle.js"></script>
    <script src="/assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript-->

    <div class="app-page-loader flex-column">
        <img alt="Logo" src="/assets/media/logos/default.svg" class="theme-light-show h-25px">
        <img alt="Logo" src="/assets/media/logos/default-dark.svg" class="theme-dark-show h-25px">

        <div class="d-flex align-items-center mt-5">
            <span class="spinner-border text-primary" role="status"></span>
            <span class="text-muted fs-6 fw-semibold ms-5">Loading...</span>
        </div>
    </div>

    <script src="https://cdn.amcharts.com/lib/5/index.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/radar.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/map.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/geodata/continentsLow.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/geodata/usaLow.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZonesLow.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZoneAreasLow.js"></script><!--!-->
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script><!--!-->
    <script src="/js/widgets.bundle.js"></script><!--!-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script><!--!-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.min.js"></script><!--!-->
    <script>
                          <!--    !-->
            function formatedCNICTextbox(id) {
                $('#' + id).mask('00000-0000000-0');
            }
        // JavaScript dialog functions removed - using pure C# Blazor approach

        window.ls = {
            set: function (key, value) {
                localStorage.setItem(key, value);
            },
            get: function (key) {
                return localStorage.getItem(key);
            }
        }
    </script><!--!-->
    <script>
                      <!--    !-->
            window.emptyBody = function() {
                document.body.className = '';
            }
        KTAppSidebar.init();
        KTThemeMode.init();
        emptyBody();
        KTComponents.init();
        //KTMenu.updateByLinkAttribute", $"/{NavigationManager.ToBaseRelativePath(NavigationManager.Uri)}");
        KTLayoutSearch.init();
    </script><!--!-->
    <!--!-->

    <script src="_framework/blazor.webview.js" autostart="false"></script>
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_app_body" class="app-default" data-kt-app-header-fixed="true" data-kt-app-header-fixed-mobile="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true">


    <!--<div class="status-bar-safe-area"></div>-->

    <div id="app">Loading...</div>

  
</body>
<!--end::Body-->

</html>

