<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Win-Election</title>
    <base href="/" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="select2.min.css" />
    <link href="css/site.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script>
        // JavaScript dialog functions removed - using pure C# Blazor approach
    </script>

        window.ls = {
            set: function (key, value) {
                localStorage.setItem(key, value);
            },
            get: function (key) {
                return localStorage.getItem(key);
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" integrity="sha512-fD9DI5bZwQxOi7MhYWnnNPlvXdp/2Pj3XSTRrFs5FQa4mizyGLnJcN6tuvUS6LbmgN1ut+XGSABKvjN0H6Aoow==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.min.js"
            type="text/javascript"></script>
    <script src="select2.min.js"></script>
    <script src="https://kit.fontawesome.com/ddd803f947.js" crossorigin="anonymous"></script>
    <script type="text/javascript">
        function formatedCNICTextbox(id) {
            $('#' + id).mask('00000-0000000-0');
        }
        //#region Select 2
        function select2Init(Id, DefaultSelectedValues, modalId) {
            var singleSelect2Selector = $('#' + Id);

            singleSelect2Selector.select2({
                width: '100%',
                dropdownParent: $(modalId)
            });

            $('#' + Id).val(DefaultSelectedValues);

            $('#' + Id).trigger('change');
        }

        function select2MultipleInit(Id, DefaultSelectedValues, modalId) {
            var multiSelect2Selector = $('#' + Id);
            multiSelect2Selector.select2({
                width: '100%',
                closeOnSelect: false,
                allowClear: false,
                dropdownParent: $(modalId)
            });

            $('#' + Id).val(DefaultSelectedValues);

            $('#' + Id).trigger('change');
        }

        function select2ComponentonChange(id, dotnetHelper, nameFunc) {
            $('#' + id).on('change', function (e) {
                dotnetHelper.invokeMethodAsync(nameFunc, $('#' + id).val(), $(this).attr("data-key"));
            });
        }

    </script>
    <!-- ApexCharts  -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Campaign Members -->
    <script>
        window.campaignMembersChart = {
            init: function (seriesValues) {
                var options = {
                    series: [seriesValues.total, seriesValues.approved, seriesValues.nonApproved],
                    chart: {
                        width: '100%',
                        type: 'donut',
                    },
                    labels: ["Total", "Approved", "Non approved"],
                    legend: {
                        position: 'bottom'
                    },
                };


                var chart = new ApexCharts(document.querySelector("#campaignmemberschart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Day Teams -->
    <script>
        window.pollingDayChart = {
            init: function (columnChartData) {
                var options = {
                    series: [{
                        name: 'Appointed',
                        data: [columnChartData.pollingAgents.pdTeamsPollingAgentsAppointed,
                        columnChartData.transportIncharge.transportInchargeAppointed,
                        columnChartData.campIncharge.camptInchargeAppointed,
                        columnChartData.foodIncharge.foodInchargeAppointed]
                    }, {
                        name: 'Target',
                        data: [columnChartData.pollingAgents.pdTeamsPollingAgentsTotal,
                        columnChartData.transportIncharge.transportInchargeTotal,
                        columnChartData.campIncharge.camptInchargeTotal,
                        columnChartData.foodIncharge.foodInchargeTotal]
                    }],
                    chart: {
                        type: 'bar',
                        width: '100%',
                        height: 170,
                        stacked: true,
                    },
                    plotOptions: {
                        bar: {
                            horizontal: true,
                            dataLabels: {
                                total: {
                                    enabled: true,
                                    offsetX: 0,
                                    style: {
                                        fontSize: '13px',
                                        fontWeight: 900
                                    }
                                }
                            }
                        },
                    },
                    stroke: {
                        width: 1,
                        colors: ['#fff']
                    },
                    xaxis: {
                        categories: ["Pollling Agents", "Transport Inc.", "Camp Inc.", "Food Inc."],
                        labels: {
                            formatter: function (val) {
                                return val + "K"
                            }
                        }
                    },
                    yaxis: {
                        title: {
                            text: undefined
                        },
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return val + "K"
                            }
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center',
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%',
                            }
                        }
                    }]
                };

                var chart = new ApexCharts(document.querySelector("#pollingdaychart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Polling Agents -->
    <script>
        window.pollingAgentChart = {
            init: function (columnChartData) {

                var options = {
                    series: [{
                        name: 'Target',
                        data: [44, 55, 41]
                    }, {
                        name: 'Marked',
                        data: [53, 32, 33]
                    }],
                    chart: {
                        type: 'bar',
                        width: '100%',
                        height: 170,
                        stacked: true,
                    },
                    plotOptions: {
                        bar: {
                            horizontal: true,
                            dataLabels: {
                                total: {
                                    enabled: true,
                                    offsetX: 0,
                                    style: {
                                        fontSize: '13px',
                                        fontWeight: 900
                                    }
                                }
                            }
                        },
                    },
                    stroke: {
                        width: 1,
                        colors: ['#fff']
                    },
                    xaxis: {
                        categories: ["Total Polling Boths", "Agents Appointed", "Agents Deficit"],
                        labels: {
                            formatter: function (val) {
                                return val + ""
                            }
                        }
                    },
                    yaxis: {
                        title: {
                            text: undefined
                        },
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return val + "K"
                            }
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center',
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%',
                            }
                        }
                    }]
                };

                var chart = new ApexCharts(document.querySelector("#pollingagentchart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Stations -->
    <script>
        window.pollingStationsChart = {
            init: function (seriesValues) {

                var options = {
                    series: [{
                        data: [seriesValues.male, seriesValues.female, seriesValues.combined],
                    }],
                    chart: {
                        type: 'bar',
                        height: 170
                    },
                    plotOptions: {
                        bar: {
                            borderRadius: 4,
                            horizontal: false,
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    xaxis: {
                        categories: ['Male', 'Female', 'Total'],
                    }
                };

                var chart = new ApexCharts(document.querySelector("#pollingstationschart"), options);
                chart.render();
            }
        }
    </script>



    <!-- Votes Chart -->
    <script type="text/javascript">
        function votesChart() {
            var options = {
                series: [{
                    name: 'Target',
                    type: 'column',
                    data: [440, 505, 414, 671, 227, 413]
                },
                {
                    name: 'Marked',
                    type: 'column',
                    data: [440, 505, 414, 671, 227, 413]
                }, {
                    name: 'Result',
                    type: 'line',
                    data: [23, 42, 35, 27, 43, 22]
                }],
                chart: {
                    width: '100%',
                    height: 250,
                    type: 'line',
                },
                stroke: {
                    width: [0, 4]
                },
                dataLabels: {
                    enabled: true,
                    enabledOnSeries: [1]
                },
                labels: ['Total Votes', 'Previous Marked', 'Previous Polled', 'Previous Confirmed', 'Beneficiaries', 'Party Members'],
                xaxis: {
                    type: 'text'
                },
                yaxis: [{


                }, {
                    opposite: true,

                }],
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };
            let chart = new ApexCharts(document.querySelector("#voteschart"), options);
            chart.render();
        }


    </script>
    <!-- Votes with Phone Numbers -->
    <script>
        function votesWithPhonesChart() {
            var options = {
                series: [190, 120, 70],
                chart: {
                    width: '100%',
                    height: 280,
                    type: 'radialBar',
                },
                plotOptions: {
                    radialBar: {
                        offsetY: 0,
                        startAngle: 0,
                        endAngle: 270,
                        hollow: {
                            margin: 5,
                            size: '30%',
                            background: 'transparent',
                            image: undefined,
                        },
                        dataLabels: {
                            name: {
                                show: true,
                            },
                            value: {
                                show: false,
                            }
                        }
                    }
                },
                // colors: ['#1ab7ea', '#0084ff', '#39539E'],
                labels: ['Total', 'Male', 'Female'],
                legend: {
                    show: true,
                    floating: true,
                    fontSize: '16px',
                    position: 'left',
                    offsetX: 20,
                    offsetY: 15,
                    labels: {
                        useSeriesColors: true,
                    },
                    markers: {
                        size: 0
                    },
                    formatter: function (seriesName, opts) {
                        return seriesName + ":  " + opts.w.globals.series[opts.seriesIndex]
                    },
                    itemMargin: {
                        vertical: 3
                    }
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        },
                        legend: {
                            offsetX: 0,
                        }
                    }
                }]
            };

            let chart = new ApexCharts(document.querySelector("#voteswithphonechart"), options);
            chart.render();
        }
    </script>
    <!-- Age Bracket -->
    <script>

        function ageBrackChart() {
            var options = {
                series: [{
                    data: [44, 55, 41],
                    name: "Total"
                }, {
                    data: [53, 32, 33],
                    name: "Female"
                }, {
                    data: [13, 44, 32],
                    name: "Male"
                }],
                chart: {
                    type: 'bar',
                    width: '100%',
                    height: 200
                },
                plotOptions: {
                    bar: {
                        dataLabels: {
                            position: 'top',
                        },
                    }
                },
                dataLabels: {
                    enabled: true,
                    offsetX: 0,
                    style: {
                        fontSize: '12px',
                        colors: ['#fff']
                    }
                },
                stroke: {
                    show: true,
                    width: 1,
                    colors: ['#fff']
                },
                tooltip: {
                    shared: true,
                    intersect: false
                },
                xaxis: {
                    categories: [2021, 2022, 2023],
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        },
                        legend: {
                            show: false
                        }
                    }
                }]
            };

            let chart = new ApexCharts(document.querySelector("#agechart"), options);
            chart.render();
        }
    </script>
    <!-- Family Bracket -->
    <script>
        function familyBreakdownChart() {
            var options = {
                series: [{
                    name: 'Votes',
                    data: [44, 55, 41]
                }, {
                    name: 'Families',
                    data: [53, 32, 33]
                }],
                chart: {
                    type: 'bar',
                    width: '100%',
                    height: 200,
                    stacked: true,
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                        dataLabels: {
                            total: {
                                enabled: true,
                                offsetX: 0,
                                style: {
                                    fontSize: '13px',
                                    fontWeight: 900
                                }
                            }
                        }
                    },
                },
                stroke: {
                    width: 1,
                    colors: ['#fff']
                },
                xaxis: {
                    categories: [2021, 2022, 2023],
                    labels: {
                        formatter: function (val) {
                            return val + "K"
                        }
                    }
                },
                yaxis: {
                    title: {
                        text: undefined
                    },
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val + "K"
                        }
                    }
                },
                fill: {
                    opacity: 1
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'left',
                    offsetX: 40
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };

            let chart = new ApexCharts(document.querySelector("#familychart"), options);
            chart.render();
        }
    </script>
    <!-- Votes Added -->
    <script>
        function votesAddedChart() {
            var options = {
                series: [76],
                chart: {
                    width: '100%',
                    height: 150,
                    type: 'radialBar',
                    offsetY: 0,

                },
                plotOptions: {
                    radialBar: {
                        startAngle: -90,
                        endAngle: 90,
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: '97%'

                        },
                        dataLabels: {
                            name: {
                                show: false
                            },
                            value: {
                                offsetY: -2,
                                fontSize: '16px'
                            }
                        }
                    }
                },
                grid: {
                    padding: {
                        top: 0
                    }
                },
                fill: {
                    color: '#405089'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };

            let chart = new ApexCharts(document.querySelector("#votesaddedchart"), options);
            chart.render();

            var options = {
                series: [0],
                chart: {
                    width: '100%',
                    height: 150,
                    type: 'radialBar',
                    offsetY: 0,

                },
                plotOptions: {
                    radialBar: {
                        startAngle: -90,
                        endAngle: 90,
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: '97%'

                        },
                        dataLabels: {
                            name: {
                                show: false
                            },
                            value: {
                                offsetY: -2,
                                fontSize: '16px'
                            }
                        }
                    }
                },
                grid: {
                    padding: {
                        top: 0
                    }
                },
                fill: {
                    color: '#405089'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };

            let chart2 = new ApexCharts(document.querySelector("#votesaddedchart1"), options);
            chart2.render();
        }

    </script>
    <!-- Votes Removed -->
    <script>
        function votesRemovedChart() {
            var options = {
                series: [50],
                chart: {
                    width: '100%',
                    height: 120,
                    type: 'radialBar',
                    offsetY: 0,

                },
                plotOptions: {
                    radialBar: {
                        startAngle: 0,
                        endAngle: 360,
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: '97%'
                        },
                        dataLabels: {
                            name: {
                                show: false
                            },
                            value: {
                                offsetY: 5,
                                fontSize: '16px'
                            }
                        }
                    }
                },
                fill: {
                    color: '#405089'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };


            let chart = new ApexCharts(document.querySelector("#votesremovedchart"), options);
            chart.render();

            var options = {
                series: [0],
                chart: {
                    width: '100%',
                    height: 120,
                    type: 'radialBar',
                    offsetY: 0,

                },
                plotOptions: {
                    radialBar: {
                        startAngle: 0,
                        endAngle: 360,
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: '97%'
                        },
                        dataLabels: {
                            name: {
                                show: false
                            },
                            value: {
                                offsetY: 5,
                                fontSize: '16px'
                            }
                        }
                    }
                },
                fill: {
                    color: '#405089'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: '100%',
                        }
                    }
                }]
            };


            let chart2 = new ApexCharts(document.querySelector("#votesremovedchart1"), options);
            chart2.render();

        }
    </script>



    <!-- Googel Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Noto+Nastaliq+Urdu:wght@400;500;700&display=swap"
          rel="stylesheet">

    <!-- AlpineJs Scripts -->
    <!-- @alpinejs/ui -->
    <script defer src="https://unpkg.com/@alpinejs/ui@3.12.3-beta.0/dist/cdn.min.js"></script>
    <!-- @alpinejs/focus -->
    <script defer src="https://unpkg.com/@alpinejs/focus@3.12.3/dist/cdn.min.js"></script>
    <!-- alpinejs -->
    <script defer src="https://unpkg.com/alpinejs@3.12.3/dist/cdn.min.js"></script>

     <script src="_framework/blazor.webview.js"></script> 



</head>
<body class="bg-gray-200" id="app">

</body>

</html>