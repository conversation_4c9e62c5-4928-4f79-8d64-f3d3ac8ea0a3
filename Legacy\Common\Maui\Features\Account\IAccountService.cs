﻿using Infodice.WE.Common.DTOs;
using Infodice.WE.Common.Features.Account.DTOs;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Infodice.WE.Common.Maui.Features.Account
{
    public interface IAccountService
    {
        public Task<PhoneNumberResponseDto> GenerateSigninPinCode(PhoneNumberRequestDto requestDto);
        public Task<SigninResponseDto> Signin(SigninRequestDto requestDto);
        public Task<ProfileStatusDto> GetProfileStatus();
        public Task<int> UpdateUserInfo(UserInfoModelDto userInfoModelDTO);
        public Task<int> UpdateProfilePicture(FileModelDTO fileModelDTO);
        public Task<UserProfileDto> GetProfile();
        public Task<bool> IsUserMemberOfTeam();
        public Task<int> AddNewUser(UserDto userDTO);
        public AppVersionDto GetVersion();
        public Task Logout();
    }
}
