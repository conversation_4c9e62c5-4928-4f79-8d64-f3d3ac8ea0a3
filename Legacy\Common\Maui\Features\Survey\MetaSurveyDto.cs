﻿using Infodice.WE.Common.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Infodice.WE.Common.Maui.Dtos.Survey
{
    public class MetaSurveyDto
    {
        public int Id { get; set; }

        public string Caption { get; set; }

        public string Description { get; set; }

        public int? PoliticalEventId { get; set; }
        public SurveyLinguisticType SurveyLinguisticType { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}
