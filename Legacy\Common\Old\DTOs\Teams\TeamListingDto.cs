﻿using System;

namespace Infodice.WE.Common.DTOs.Teams
{
    public class TeamListingModel
    { 
        public int Id { get; set; }

        public string UserId { get; set; }

        public string UserName { get; set; }

        public string FullName { get; set; }

        public bool IsActive { get; set; }

        public string ImageString { get; set; }

        public byte[] Image { get; set; }

        public string TeamName { get; set; }

        public string Designation { get; set; }

        public int TeamId { get; set; }

        public DateTimeOffset CreateAt { get; set; }
    }
}
