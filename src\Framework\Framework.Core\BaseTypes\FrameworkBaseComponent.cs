﻿using Framework.Core.UIServices;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.JSInterop;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Framework.Core
{
    public class FrameworkBaseComponent : ComponentBase
    {
        [Inject]
        public IAuthenticatedUser? AuthenticatedUser { get; set; }

        [Inject]
        public MessageCenter? MessageCenter { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; } = null!;

        [Parameter]
        public ModalDialogConfig? DialogConfig { get; set; }

        [Parameter]
        public string? OperationId { get; set; }


        [Inject]
        public DialogService DialogService { get; set; } = null!;

        [Inject]
        public NotificationService NotificationService { get; set; } = null!;

        [Inject]
        protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

        [Inject]
        public IJSRuntime JsRuntime { get; set; }

        [Inject]
        public AuthenticationStateProvider? AuthenticationStateProvider { get; set; }
        public ClaimsPrincipal User
        {
            get
            {
                return _user;
            }
            set
            {
                _user = value;
                if (AuthenticatedUser != null && _user != null)
                {
                    AuthenticatedUser.UserId = _user.GetUserId();
                    AuthenticatedUser.UserName = _user.GetUsername();
                    AuthenticatedUser.Roles = _user.GetRoles();
                }
            }
        }

        private ClaimsPrincipal _user;

        //todo
        //[Inject]
        //public IAuthenticatedUser? AuthenticatedUser { get; set; }

        [Inject]
        public AuthenticationStateProvider? authenticationStateProvider { get; set; }
         
        protected KeyValuePair<string, object> P(object? value, [CallerArgumentExpression("value")] string? variableName = null)
        {
            return new KeyValuePair<string, object>(variableName, value);
        }


        public enum Size_
        {
            Sm,
            Md,
            Xl,
            Xl2,
            Xl3,
            Xl4,
            Xl5,
            Xl6,
            Xl7,
            Xl8,
        }

        public enum Position_
        {
            None = 0,
            Right,
            Center
        }

        /// <summary>
        /// Method to show a dialog with specified parameters.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="title"></param>
        /// <param name="id"></param>
        /// <param name="dialogSize_"></param>
        /// <param name="position"></param>
        /// <param name="showCrossIcon"></param>
        /// <param name="parameters"></param>
        /// <exception cref="Exception"></exception>
        protected void ShowDialog<T>(string? title, object? id, Size_ dialogSize_ = Size_.Xl,
               Position_ position = Position_.Right, bool showCrossIcon = true,
               params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {

            var config = new ModalDialogConfig()
            {
                Component = typeof(T),
                Title = title,
                ShowCrossIcon = true,
            };

            var parameters_ = parameters.ToDictionary();
            parameters_.Add("DialogConfig", config);
            parameters_.Add("Id", id);
            parameters_.Add("OperationId", OperationId);

            if (DialogService == null)
                throw new Exception("Dialog service is not initialized");

            switch (position)
            {
                case Position_.Center:
                    config.PositionClasses = "justify-center";
                    config.DialogContainerClasses = "grid place-items-center";
                    break;
                case Position_.Right:
                    config.PositionClasses = "justify-end min-h-[calc(100%-0.5rem)] h-[calc(100%-0.5rem)]";
                    config.DialogContainerClasses = "flex justify-end";
                    break;
            }
            switch (dialogSize_)
            {
                case Size_.Sm:
                    config.SizeClasses = "md:max-w-sm";
                    break;
                case Size_.Md:
                    config.SizeClasses = "md:max-w-md";
                    break;
                case Size_.Xl:
                    config.SizeClasses = "md:max-w-xl";
                    break;
                case Size_.Xl2:
                    config.SizeClasses = "md:max-w-2xl";
                    break;
                case Size_.Xl3:
                    config.SizeClasses = "md:max-w-3xl";
                    break;
                case Size_.Xl4:
                    config.SizeClasses = "md:max-w-4xl";
                    break;
                case Size_.Xl5:
                    config.SizeClasses = "md:max-w-5xl";
                    break;
                case Size_.Xl6:
                    config.SizeClasses = "md:max-w-6xl";
                    break;
                case Size_.Xl7:
                    config.SizeClasses = "md:max-w-7xl";
                    break;
                case Size_.Xl8:
                    config.SizeClasses = "md:max-w-screen-2xl";
                    break;
            }

            config.Parameters = parameters_!;
            DialogService.ShowDialogAsync(config);
            StateHasChanged();

        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowCenterWideDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl2, Position_.Center, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowCenterFullDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl, Position_.Center, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowCenterMediumDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl, Position_.Center, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowSideDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl, Position_.Right, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowSideMediumDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl, Position_.Right, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowSideWideDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl2, Position_.Right, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        protected void ShowCenterMudDialog<T, TKey>(string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size_.Xl, Position_.Center, true, parameters);
        }

        [Obsolete("Use ShowDialog<T> instead")]
        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        [Obsolete("Use ShowDialog<T> instead")]
        public async Task ClientLog(string Text)
        {
            await JsRuntime.InvokeVoidAsync("browserLog", Text);
        }
         
        public event PropertyChangedEventHandler? PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void SetProperty<T>(ref T property, T value, [CallerMemberName] string propertyName = "")
        {
            if ((property == null && value != null) || !property.Equals(value))
            {
                property = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
        protected override async Task OnInitializedAsync()
        {
            var authenticationState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authenticationState != null)
            {
                if (authenticationState.User.Identity.IsAuthenticated)
                {
                    User = authenticationState.User;
                }
            }

        }
    }
}
