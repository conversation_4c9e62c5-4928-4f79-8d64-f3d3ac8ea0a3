﻿@using Framework.Core
@using Microsoft.AspNetCore.Components.Authorization
@inherits FrameworkLayoutBaseComponent

@inject NavigationManager NavigationManager
<AuthorizeView>
    <Authorized>
     
        @Body
      
    </Authorized>
    <NotAuthorized>
        @{
            NavigationManager?.NavigateTo($"/identity/account/login", true);
        }
    </NotAuthorized>
</AuthorizeView>

@if (DialogService != null && DialogService.Dialogs.Count > 0)
{

    @foreach (var dialog in DialogService.Dialogs)
    {

        string classes = "";
        switch (dialog.Direction)
        {
            case Framework.Core.UIServices.DialogType.CenterMedium:
                classes = "dw-md rz-dialog-side-position-center"; break;

            case Framework.Core.UIServices.DialogType.CenterWide:
                classes = "dw-lg rz-dialog-side-position-center square"; break;

            case Framework.Core.UIServices.DialogType.RightMedium:
                classes = "dw-md rz-dialog-side-position-right square"; break;

            case Framework.Core.UIServices.DialogType.RightWide:
                classes = "dw-lg rz-dialog-side-position-right"; break;

            case Framework.Core.UIServices.DialogType.CenterFull:
                classes = "dw-full rz-dialog-side-position-center square"; break;
        }

        <div id="@dialog.Id" class='rz-dialog-wrapper custom-dialog-wrapper-class'>
            <aside class="rz-dialog-side  @classes">
                @if (!string.IsNullOrEmpty(dialog.Title))
                {
                    <div class="sticky top-0 flex items-center justify-between p-2 md:p-4 bg-white border-b z-40">
                        <h3 class="text-sm md:text-xl font-semibold text-gray-900 truncate">
                            @dialog.Title
                        </h3>
                        <button @onclick='()=> CloseMe(dialog)' type="button" class="text-gray-400  hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                            <svg aria-hidden="true" class="h-4 w-4 md:w-5 md:h-5 text-gray-900" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                }
                <!-- Modal body -->
                <div class="flex flex-col gap-3 px-3 pt-3 md:pt-6 md:px-6 md:gap-10 bg-white">
                    <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                </div>
            </aside>
            <div class="rz-dialog-mask"></div>
        </div>
    }

    <div class="hidden bg-green-200"></div>
}
