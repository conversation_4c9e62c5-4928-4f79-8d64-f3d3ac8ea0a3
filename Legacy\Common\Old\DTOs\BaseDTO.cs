﻿using System.Linq;
using System.Net.Http;
using System.Reflection;

namespace Infodice.WE.Common.DTOs
{
    public class T
    {
        public T()
        {

        }

        public T(long sts)
        {
            STS = sts;
        }

        public long STS { get; set; }

        public FormUrlEncodedContent ToFormData()
        {
            var properties = GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public)
             .ToDictionary(prop => prop.Name, prop => prop.GetValue(this, null)?.ToString());
            return new FormUrlEncodedContent(properties);
        }

        public string ToQueryString()
        {
            var properties = GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Select(x => $"{x.Name}={x.GetValue(this, null)}");
            return string.Join("&", properties);
             
        }

        public static string ToQueryString(object obj)
        {
            var properties = obj.GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Select(x => $"{x.Name}={x.GetValue(obj, null)}");
            return string.Join("&", properties);

        }
 
    }
}
