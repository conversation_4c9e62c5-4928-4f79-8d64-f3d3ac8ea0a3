﻿using Infodice.WE.Common.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Infodice.WE.Common.Maui.Dtos.Survey
{
    public class MetaSurveyQuestionDto
    {
        public int Id { get; set; }

        /// <summary>
        /// In case of Tablular question, multiple child question will refer to a parent question. 
        /// e.g. Parent Question 	اس حلقے کی سب سے بڑی برادری کونسی ہے 
        /// 3 Child Questions (each question is a table column)
        /// |    Type Radio Button      برادری کا تناسب      |    Type Drop Down      پارٹی وابستگی    |    Type Open Text   برادری
        /// Importantly, In survey execution form, multiple occurances of these questions will be acheived via (+) button in table.
        /// </summary>
        public int? ParentId { get; set; }
        public int MetaSurveyId { get; set; }


        public int MetaSurveySectionId { get; set; }


        public string Text { get; set; }

        public AnswerType AnswerType { get; set; }

        public QuertionCategory QuertionCategory { get; set; }

        public ChartType ChartType { get; set; }

        public byte SortOrderForDashboard { get; set; }

        public byte SortOrderForQuestionnaire { get; set; }

        public string CssClasses { get; set; }

        public bool ShouldActAsFilter { get; set; }

        public bool IsActiveForQuestionnaire { get; set; }

        public bool IsActiveForDashboard { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}
