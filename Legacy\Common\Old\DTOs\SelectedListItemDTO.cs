﻿namespace Infodice.WE.Common.DTOs
{
    public class SelectedListItemDto
    {
        public SelectedListItemDto()
        {

        }
        public SelectedListItemDto(int value, string text)
        {
            IntValue = value;
            Text = text;
        }

        public SelectedListItemDto(int value, string text, string AdditionalValue)
        {
            IntValue = value;
            Text = text;
            this.AdditionalValue = AdditionalValue;
        }

        public SelectedListItemDto(string value, string text, string AdditionalValue)
        {
            Value = value;
            Text = text;
            this.AdditionalValue = AdditionalValue;
        }
        public SelectedListItemDto(string value, string text)
        {
            Value = value;
            Text = text;
        }

        public int? IntValue
        {
            get
            {
                if (int.TryParse(Value, out int value))
                {
                    return value;
                }
                return null;
            }
            set
            {
                Value = value?.ToString();
            }
        }
        public string AdditionalValue { get; set; }
        public string Value { get; set; }
        public string Text { get; set; }

        public int SortOrder { get; set; }
         
    }
}
