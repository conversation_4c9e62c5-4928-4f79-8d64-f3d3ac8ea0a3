﻿
using Infodice.WE.Common.Enums;
using System;

namespace Infodice.WE.Common.DTOs
{

    /// <summary>
    /// Complete Voter DTO used to download complete voter information from server via APIs
    /// </summary>
    public class VoterDTO
    {
        #region common properties
        /// <summary>
        /// VoterID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// Vote ID
        /// </summary>
        public int VoteId { get; set; }

        public long NationalId { get; set; }
     
        /// <summary>
        /// Gender
        /// </summary>
        public Gender Gender { get; set; }

        /// <summary>
        /// Phone
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// PK: Polling Station
        /// CA: PollLocation
        /// </summary>
        public string PS { get; set; }

        public string PSCode { get; set; }

        /// <summary>
        /// PK: Block Code
        /// CA: PollDiv
        /// </summary>
        public string BlockCode { get; set; }

        /// <summary>
        /// BlockCodeId
        /// </summary>
        public int BlockCodeId { get; set; }

        public int BlockCodeDpId { get; set; }

        /// <summary>
        /// PK: Electoral Area 
        /// </summary>
        public string Area { get; set; }

        /// <summary>
        /// PK: Union Council
        /// CA: Intermediate Area
        /// </summary>
        public string UC { get; set; }

        /// <summary>
        /// PK: NA
        /// CA: Riding
        /// </summary>
        public string NA { get; set; }


        #endregion

        #region CA

        public string Name { get; set; }

        /// <summary>
        /// Home Address
        /// </summary>
        public string Address { get; set; }


        public string PollAddress { get; set; }

        public string City { get; set; }
         
        public double Lat { get; set; }

        public double Long_ { get; set; }


        #endregion

        #region PK

        public int PkVoteId { get; set; }
          
        public short SNo { get; set; }

        public string GNo { get; set; }

        public byte[] NameBytes { get; set; }

        public byte[] FNameBytes { get; set; }

        public byte Age { get; set; }

        public byte[] AddressBytes { get; set; }

        /// <summary>
        /// Provincial Constituency
        /// </summary>
        public string PA { get; set; }

        public int PartyId { get; set; }

        public PartyAffiliation PAFL { get; set; }


        /// <summary>
        /// Timestamp in millis when last records was updated
        /// </summary>
        public long TS { get; set; }

        /// <summary>
        /// Row version number
        /// </summary>
        public byte RV { get; set; }

        /// <summary>
        /// Updated by
        /// </summary>
        public string UpdatedBy { get; set; }
         
        //public Color Color { get; set; }

        #endregion

        public int? PollingStationId { get; set; }

        public int? NAId { get; set; }

        public int? PAId { get; set; }

        public int ElectionId { get; set; }

        public DateTimeOffset? UpdateAt { get; set; }
          
        public RelationshipType RelationshipType { get; set; }

        public MemberShipType MemberShipType { get; set; }
        public long ReadTimeStamp { get; set; }
         
        public int PreviousPartyId { get; set; }
        public int PolledParty_Id { get; set; }

        public DateTime EventDate { get; set; }

        public bool ShowVoteMarking { get; set; }

        public int PersonId { get; set; }

    }


    /// <summary>
    /// Contains Images field of a voter
    /// </summary>
    public class VoterImagesDTO
    {
        public long Id { get; set; }

        public byte[] Name { get; set; }

        public byte[] FName { get; set; }

        public byte[] Adrress { get; set; }

    }
}
