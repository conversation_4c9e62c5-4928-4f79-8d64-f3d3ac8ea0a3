<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>WinElection</title>
    <base href="/" />
    <link rel="stylesheet" href="select2.min.css" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="fluent.css" rel="stylesheet" />
    <link href="app.css" rel="stylesheet" />

    <link rel="icon" type="image/png" href="favicon.png" />
    <style>

        .font-urdu {
            font-family: Noto Nastaliq Urdu,serif;
            line-height: 1.5rem !important;
        }

        .validation-message {
            color: darkred;
        }

        .select2-container--default .select2-selection--single {
            padding: 4px 0 !important;
            height: 2.5rem !important;
            background-color: #f9fafb !important;
            border-color: #d1d5db !important;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script>
        // JavaScript dialog functions removed - using pure C# Blazor approach
    </script>

        window.ls = {
            set: function (key, value) {
                localStorage.setItem(key, value);
            },
            get: function (key) {
                return localStorage.getItem(key);
            }
        }
        $(document).on('select2:open', () => {
            document.querySelector('.select2-search__field').focus();
        });


        window.downloadFileFromStream = async (fileName, contentStreamReference) => {
            const arrayBuffer = await contentStreamReference.arrayBuffer();
            const blob = new Blob([arrayBuffer]);
            const url = URL.createObjectURL(blob);
            const anchorElement = document.createElement('a');
            anchorElement.href = url;
            anchorElement.download = fileName ?? '';
            anchorElement.click();
            anchorElement.remove();
            URL.revokeObjectURL(url);
        }


        function saveAsFile(filename, bytesBase64) {
            if (navigator.msSaveBlob) {
                //Download document in Edge browser
                var data = window.atob(bytesBase64);
                var bytes = new Uint8Array(data.length);
                for (var i = 0; i < data.length; i++) {
                    bytes[i] = data.charCodeAt(i);
                }
                var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
                navigator.msSaveBlob(blob, filename);
            }
            else {
                var link = document.createElement('a');
                link.download = filename;
                link.href = "data:application/octet-stream;base64," + bytesBase64;
                document.body.appendChild(link); // Needed for Firefox
                link.click();
                document.body.removeChild(link);
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.min.js"
            type="text/javascript"></script>
    <script src="select2.min.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>
    <script src="https://kit.fontawesome.com/ddd803f947.js" crossorigin="anonymous"></script>
    <script type="text/javascript">
        function formatedCNICTextbox(id) {
            $('#' + id).mask('00000-0000000-0');
        }
        //#region Select 2
        function select2Init(Id, DefaultSelectedValues, modalId) {
            var singleSelect2Selector = $('#' + Id);

            singleSelect2Selector.select2({
                width: '100%',
                dropdownParent: $(modalId)
            });

            $('#' + Id).val(DefaultSelectedValues);

            $('#' + Id).trigger('change');
        }

        function select2MultipleInit(Id, DefaultSelectedValues, modalId) {
            var multiSelect2Selector = $('#' + Id);
            multiSelect2Selector.select2({
                width: '100%',
                closeOnSelect: false,
                allowClear: false,
                dropdownParent: $(modalId)
            });

            $('#' + Id).val(DefaultSelectedValues);

            $('#' + Id).trigger('change');
        }

        function select2ComponentonChange(id, dotnetHelper, nameFunc) {
            $('#' + id).on('change', function (e) {
                dotnetHelper.invokeMethodAsync(nameFunc, $('#' + id).val(), $(this).attr("data-key"));
            });
        }

        function select2Destroy(id) {
            $('#' + id).select2('destroy');
        };

        window.downloadFileFromStream = async (fileName, contentStreamReference) => {
            const arrayBuffer = await contentStreamReference.arrayBuffer();
            const blob = new Blob([arrayBuffer]);
            const url = URL.createObjectURL(blob);
            const anchorElement = document.createElement('a');
            anchorElement.href = url;
            anchorElement.download = fileName ?? '';
            anchorElement.click();
            anchorElement.remove();
            URL.revokeObjectURL(url);
        }
    </script>
    <!-- ApexCharts  -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Campaign Members -->
    <script>
        window.campaignMembersChart = {
            init: function (seriesValues) {
                var options = {
                    series: [seriesValues.total, seriesValues.approved],
                    chart: {
                        width: '100%',
                        type: 'donut',
                    },
                    labels: ["Required", "Approved Users"],
                    legend: {
                        position: 'bottom'
                    },
                };


                var chart = new ApexCharts(document.querySelector("#campaignmemberschart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Day Teams -->
    <script>
        window.pollingDayChart = {
            init: function (columnChartData) {
                var options = {
                    series: [{
                        name: 'Appointed',
                        data: [columnChartData.pollingAgents.pdTeamsPollingAgentsAppointed,
                        columnChartData.transportIncharge.transportInchargeAppointed,
                        columnChartData.campIncharge.camptInchargeAppointed,
                        columnChartData.foodIncharge.foodInchargeAppointed]
                    }, {
                        name: 'Target',
                        data: [columnChartData.pollingAgents.pdTeamsPollingAgentsTotal,
                        columnChartData.transportIncharge.transportInchargeTotal,
                        columnChartData.campIncharge.camptInchargeTotal,
                        columnChartData.foodIncharge.foodInchargeTotal]
                    }],
                    chart: {
                        type: 'bar',
                        width: '100%',
                        height: 170,
                        stacked: true,
                    },
                    plotOptions: {
                        bar: {
                            borderRadius: 6,
                            columnWidth: '50%',
                            horizontal: true,
                            dataLabels: {
                                total: {
                                    enabled: true,
                                    offsetX: 0,
                                    style: {
                                        fontSize: '13px',
                                        fontWeight: 900
                                    }
                                }
                            }
                        },
                    },
                    colors: ['#38B29B', '#bbb'],
                    xaxis: {
                        categories: ["Political Incharge", "Transport Inc.", "Camp Inc.", "Food Inc."]
                    },
                    yaxis: {
                        title: {
                            text: undefined
                        },
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return val + "K"
                            }
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center',
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%',
                            }
                        }
                    }]
                };

                var chart = new ApexCharts(document.querySelector("#pollingdaychart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Polling Agents -->
    <script>
        window.pollingAgentChart = {
            init: function (columnChartData) {

                var options = {
                    series: [{
                        name: 'Male',
                        data: [44, 20, 24]
                    }, {
                        name: 'Female',
                        data: [40, 15, 35]
                    }],
                    chart: {
                        type: 'bar',
                        width: '100%',
                        height: 170,
                        stacked: true,
                    },
                    plotOptions: {
                        bar: {
                            borderRadius: 6,
                            horizontal: true,
                            dataLabels: {
                                total: {
                                    enabled: true,
                                    offsetX: 0,
                                    style: {
                                        fontSize: '13px',
                                        fontWeight: 900
                                    }
                                }
                            }
                        },
                    },
                    colors: ['#3F4F88', '#DB6767'],
                    xaxis: {
                        categories: ["Total Polling Boths", "Agents Appointed", "Agents Deficit"],
                        labels: {
                            formatter: function (val) {
                                return val;
                            }
                        }
                    },
                    yaxis: {
                        title: {
                            text: undefined
                        },
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return val;
                            }
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center',
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%',
                            }
                        }
                    }]
                };

                var chart = new ApexCharts(document.querySelector("#pollingagentchart"), options);
                chart.render();
            }
        }
    </script>
    <!-- Polling Stations -->
    <script>
        window.pollingStationsChart = {
            init: function (seriesValues) {

                var options = {
                    series: [{
                        data: [seriesValues.male, seriesValues.female, seriesValues.combined],
                    }],
                    chart: {
                        type: 'bar',
                        height: 170
                    },
                    plotOptions: {
                        bar: {
                            borderRadiusApplication: 'around',
                            borderRadiusWhenStacked: 'last',
                            borderRadius: 8,
                            columnWidth: '20%', horizontal: false,
                            dataLabels: {
                                position: 'top',
                            }

                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    colors: ['#38B29B', '#3F4F88', '#DB6767'],
                    xaxis: {
                        categories: ['Male', 'Female', 'Total'],
                    }
                };

                var chart = new ApexCharts(document.querySelector("#pollingstationschart"), options);
                chart.render();
            }
        }
    </script>

    <script type="text/javascript">
        window.BlazorInputFile = {
            init: function init(elem, componentInstance) {
                var nextFileId = 0;

                elem.addEventListener('change', function handleInputFileChange(event) {
                    // Reduce to purely serializable data, plus build an index by ID
                    elem._blazorFilesById = {};
                    var fileList = Array.prototype.map.call(elem.files, function (file) {
                        var result = {
                            id: ++nextFileId,
                            lastModified: new Date(file.lastModified).toISOString(),
                            name: file.name,
                            size: file.size,
                            type: file.type
                        };
                        elem._blazorFilesById[result.id] = result;

                        // Attach the blob data itself as a non-enumerable property so it doesn't appear in the JSON
                        Object.defineProperty(result, 'blob', { value: file });

                        return result;
                    });

                    componentInstance.invokeMethodAsync('NotifyChange', fileList).then(null, function (err) {
                        throw new Error(err);
                    });
                });
            },

            readFileData: function readFileData(elem, fileId, startOffset, count) {
                var readPromise = getArrayBufferFromFileAsync(elem, fileId);

                return readPromise.then(function (arrayBuffer) {
                    var uint8Array = new Uint8Array(arrayBuffer, startOffset, count);
                    var base64 = uint8ToBase64(uint8Array);
                    return base64;
                });
            },

            ensureArrayBufferReadyForSharedMemoryInterop: function ensureArrayBufferReadyForSharedMemoryInterop(elem, fileId) {
                return getArrayBufferFromFileAsync(elem, fileId).then(function (arrayBuffer) {
                    getFileById(elem, fileId).arrayBuffer = arrayBuffer;
                });
            },

            readFileDataSharedMemory: function readFileDataSharedMemory(readRequest) {
                // This uses various unsupported internal APIs. Beware that if you also use them,
                // your code could become broken by any update.
                var inputFileElementReferenceId = Blazor.platform.readStringField(readRequest, 0);
                var inputFileElement = document.querySelector('[_bl_' + inputFileElementReferenceId + ']');
                var fileId = Blazor.platform.readInt32Field(readRequest, 4);
                var sourceOffset = Blazor.platform.readUint64Field(readRequest, 8);
                var destination = Blazor.platform.readInt32Field(readRequest, 16);
                var destinationOffset = Blazor.platform.readInt32Field(readRequest, 20);
                var maxBytes = Blazor.platform.readInt32Field(readRequest, 24);

                var sourceArrayBuffer = getFileById(inputFileElement, fileId).arrayBuffer;
                var bytesToRead = Math.min(maxBytes, sourceArrayBuffer.byteLength - sourceOffset);
                var sourceUint8Array = new Uint8Array(sourceArrayBuffer, sourceOffset, bytesToRead);

                var destinationUint8Array = Blazor.platform.toUint8Array(destination);
                destinationUint8Array.set(sourceUint8Array, destinationOffset);

                return bytesToRead;
            }
        };

        function getArrayBufferFromFileAsync(elem, fileId) {
            var file = getFileById(elem, fileId);

            // On the first read, convert the FileReader into a Promise<ArrayBuffer>
            if (!file.readPromise) {
                file.readPromise = new Promise(function (resolve, reject) {
                    var reader = new FileReader();
                    reader.onload = function () { resolve(reader.result); };
                    reader.onerror = function (err) { reject(err); };
                    reader.readAsArrayBuffer(file.blob);
                });
            }

            return file.readPromise;
        }


        function getFileById(elem, fileId) {
            var file = elem._blazorFilesById[fileId];
            if (!file) {
                throw new Error('There is no file with ID ' + fileId + '. The file list may have changed');
            }

            return file;
        }

        var uint8ToBase64 = (function () {
            // Code from https://github.com/beatgammit/base64-js/
            // License: MIT
            var lookup = [];

            var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            for (var i = 0, len = code.length; i < len; ++i) {
                lookup[i] = code[i];
            }

            function tripletToBase64(num) {
                return lookup[num >> 18 & 0x3F] +
                    lookup[num >> 12 & 0x3F] +
                    lookup[num >> 6 & 0x3F] +
                    lookup[num & 0x3F];
            }

            function encodeChunk(uint8, start, end) {
                var tmp;
                var output = [];
                for (var i = start; i < end; i += 3) {
                    tmp =
                        ((uint8[i] << 16) & 0xFF0000) +
                        ((uint8[i + 1] << 8) & 0xFF00) +
                        (uint8[i + 2] & 0xFF);
                    output.push(tripletToBase64(tmp));
                }
                return output.join('');
            }

            return function fromByteArray(uint8) {
                var tmp;
                var len = uint8.length;
                var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes
                var parts = [];
                var maxChunkLength = 16383; // must be multiple of 3

                // go through the array every three bytes, we'll deal with trailing stuff later
                for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
                    parts.push(encodeChunk(
                        uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)
                    ));
                }

                // pad the end with zeros, but make sure to not forget the extra bytes
                if (extraBytes === 1) {
                    tmp = uint8[len - 1];
                    parts.push(
                        lookup[tmp >> 2] +
                        lookup[(tmp << 4) & 0x3F] +
                        '=='
                    );
                } else if (extraBytes === 2) {
                    tmp = (uint8[len - 2] << 8) + uint8[len - 1];
                    parts.push(
                        lookup[tmp >> 10] +
                        lookup[(tmp >> 4) & 0x3F] +
                        lookup[(tmp << 2) & 0x3F] +
                        '='
                    );
                }

                return parts.join('');
            };
        })();


    </script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Noto+Nastaliq+Urdu:wght@400;500;700&display=swap"
          rel="stylesheet">

    <!-- AlpineJs Scripts -->
    <!-- @alpinejs/ui -->
    <script defer src="https://unpkg.com/@alpinejs/ui@3.12.3-beta.0/dist/cdn.min.js"></script>
    <!-- @alpinejs/focus -->
    <script defer src="https://unpkg.com/@alpinejs/focus@3.12.3/dist/cdn.min.js"></script>
    <!-- alpinejs -->
    <script defer src="https://unpkg.com/alpinejs@3.12.3/dist/cdn.min.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication/AuthenticationService.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>

</head>

<body class="bg-gray-200">

</body>

</html>
