﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Infodice.WE.Common.Dtos
{
    public class RequestDto
    {
        public string ToQueryString()
        {
            var properties = GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Select(x => $"{x.Name}={x.GetValue(this, null)}");
            return "?" + string.Join("&", properties);

        }
    }
}
