﻿@using Framework.Core
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@inherits FrameworkLayoutBaseComponent
<style>
    .validation-message {
        color: firebrick;
    }

    .drawer {
        background-color: #052408;
    }

    .selected-span {
        background: #ECBA0A;
        border-radius: 4px;
    }

</style>
<MudThemeProvider Theme="MyCustomTheme" />
<MudDialogProvider />
<MudSnackbarProvider />
<AuthorizeView>
    <Authorized>
        <MudLayout>
            <MudAppBar>
                <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())"> </MudIconButton>
                Vote Search
                <img src="images/mianbros.png" style="height:48px; right:8px; position:fixed;" alt="bros">
            </MudAppBar>
            <MudDrawer @bind-Open="@_drawerOpen" Class="drawer">
                <img src="images/logo.svg" class="mt-8 mb-4" style="height:48px;" />
                <div class="text-green-800 w-full my-2 text-center text-xs font-normal font-['Poppins'] leading-tight">@(typeof(MainLayout).Assembly.GetName().Version.ToString())</div>
                @{
                    var url = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);

                }
                <ul class="flex flex-col gap-1 mb-8 ">
                    @* <MenuItem Caption="Home" Href="/home" IconPrefix="home" IsActive=@( url.StartsWith("home")) /> *@
                    <MenuItem Caption="Vote Search" Href="/search" IconPrefix="vote_search" IsActive=@(url == "" ||url == "search") />
                    @*  <MenuItem Caption="Offline Data" Href="/offlinedata" IconPrefix="offline_data" IsActive=@(url == "offlinedata") /> *@ 
                    <MenuItem Caption="Profile" Href="/profile/detail" IconPrefix="profile" IsActive=@(url == "profile/detail") />
                    <MenuItem Caption="Logout" Href="/identity/account/login" IconPrefix="logout" IsActive=@(url == "identity/account/login") />
                     
                    

                </ul>
                <MudDivider Class="my-2 border-none" />

                <div class="w-[296px] h-[91px] px-4 pb-6 flex-col justify-start items-start gap-5 inline-flex">
                    <div class="self-stretch pl-2 pr-8 pt-6 border-t border-zinc-800 justify-start items-center gap-[47px] inline-flex">
                        <div class="grow shrink basis-0 h-[43px] justify-start items-center gap-3 flex">
                            <div class="w-10 h-10 flex-col justify-center items-center inline-flex">
                                <img class="w-14" src="https://via.placeholder.com/45x45" style="border-radius:100px;" />
                            </div>
                            <div class="flex-col justify-start items-start gap-[3px] inline-flex">
                                <div class="text-white text-sm font-semibold font-['Poppins'] leading-tight">Test User</div>
                                <div class="text-gray-300 text-sm font-normal font-['Poppins'] leading-tight">@context.User.Identity.Name</div> 
                            </div>
                        </div>
                    </div>
                </div>

            </MudDrawer>
            <MudMainContent>
                @Body
            </MudMainContent>
            @if (DialogService != null && DialogService.Dialogs.Count > 0)
            {

                @foreach (var dialog in DialogService.Dialogs)
                {
                    @if (dialog.Direction == Framework.Core.UIServices.DialogType.CenterMud)
                    {
                        <div class="mud-overlay mud-overlay-dialog" style="background-color:#8f8f8f63;">
                            <div id="@dialog.Id" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="mud-dialog mud-dialog-width-md mud-dialog-width-full">
                                <div class="mud-dialog-title"><!--!--><h6 class="mud-typography mud-typography-h6">@dialog.Title</h6></div>
                                <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                            </div>
                        </div>
                    }
                    else
                    {
                        string classes = "";
                        switch (dialog.Direction)
                        {
                            case Framework.Core.UIServices.DialogType.CenterMedium:
                                classes = "position-relative w-96 mx-auto my-auto p-0 bg-white shadow relative max-w-full h-fit max-h-[100dvh] transform transition-all duration-150 ease-in-out scale-100 opacity-100"; break;
                            case Framework.Core.UIServices.DialogType.CenterWide:
                                classes = "position-relative w-[48rem] mx-auto my-auto p-0 bg-white shadow relative max-w-full h-fit max-h-[100dvh] transform transition-all duration-150 ease-in-out scale-100 opacity-100"; break;
                            case Framework.Core.UIServices.DialogType.RightMedium:
                                classes = "relative max-w-xl p-0 bg-white shadow start-auto h-auto max-h-[100dvh] w-96 transform transition-all duration-150 ease-in-out"; break;

                        }
                        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">

                            <div id="@dialog.Id" class='@classes'>
                                <div>
                                    @if (!string.IsNullOrEmpty(dialog.Title))
                                    {
                                        <div class="sticky top-0 flex items-center justify-between p-2 md:p-4 bg-white border-b rounded-t z-40">
                                            <h3 class="text-sm md:text-xl font-semibold text-gray-900 truncate">
                                                @dialog.Title
                                            </h3>
                                            <button @onclick='()=> CloseMe(dialog)' type="button" class="text-gray-400  hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                                                <svg aria-hidden="true" class="h-4 w-4 md:w-5 md:h-5 text-gray-900" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                                <span class="sr-only">Close modal</span>
                                            </button>
                                        </div>
                                    }
                                    <!-- Modal body -->
                                    <div class="flex flex-col gap-3 md:gap-10">
                                        <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }

                <div class="hidden bg-green-200"></div>
            }
        </MudLayout>

    </Authorized>
    <NotAuthorized>
        @{
            NavigationManager?.NavigateTo($"/identity/account/login", true);
        }
    </NotAuthorized>
</AuthorizeView>



