﻿@using Framework.Core.UIServices;
@using Microsoft.JSInterop;
@if (DialogService != null)
{
    @foreach (var dialog in DialogService.Dialogs)
    {
        <div class="modal fade show" id="@dialog.Id" tabindex="-1" style="display: block; padding-left: 0px;" aria-modal="true" role="dialog">
            <div class="modal-dialog modal-xl">
                <div class="modal-content rounded">
                    <div class="modal-header justify-content-end border-0 pb-0">
                        <div @onclick='()=> CloseMe(dialog)' class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                            <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                        </div>
                    </div>
                    <div class="modal-body pt-0 pb-15 px-5 px-xl-20">
                        <div class="mb-13 text-center">
                            <h1 class="mb-3">@dialog.Title</h1>
                        </div>
                        <div class="d-flex flex-column">
                            <CascadingValue Value="dialog">
                                <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                            </CascadingValue>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        _ = ShowModal(dialog.Id);
    }
}
@code {

    [Inject]
    IJSRuntime JsRuntime { get; set; }

    private DialogService? _dialogService;
    [Inject]
    public DialogService? DialogService
    {
        get { return _dialogService; }
        set
        {
            if (_dialogService != value)
            {
                _dialogService = value;
                if (_dialogService != null)
                {
                    _dialogService.Dialogs.CollectionChanged += Dialogs_CollectionChanged;
                }
            }
        }
    }
    private void Dialogs_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
    {
        StateHasChanged();
    }
    void ShowModal(string id)
    {
        // Pure C# Blazor approach - dialog visibility is controlled by CSS classes
        // No JavaScript needed as dialogs are rendered based on DialogService.Dialogs collection
        StateHasChanged();
    }

    private void CloseMe(ModalDialogConfig dialogConfig)
    {
        // Pure C# Blazor dialog close mechanism
        DialogService!.Dialogs.Remove(dialogConfig);
        StateHasChanged();
    }

}
