<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>VoteUnit MobileApp</title>
    <base href="/" />


    <!-- <PERSON><PERSON>onts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />

    <link href="app.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />

    <style>
        .urdu {
            font-family: 'Noto Naskh Arabic', serif;
            font-size: 0.9rem;
            line-height: 2;
        }
    </style>
</head>

<body>
    <div id="app" class="w-full m-auto md:max-w-sm"> </div>
    <script>
       window.ls = {
            set: function (key, value) {
                localStorage.setItem(key, value);
            },
            get: function (key) {
                return localStorage.getItem(key);
            }
        }
    </script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>

    <script>
        // JavaScript dialog functions removed - using pure C# Blazor approach
    </script>
    </script>
    <script src="_framework/blazor.webassembly.js"></script>

</body>
</html >
