﻿using Infodice.WE.Common.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Infodice.WE.Common.Maui.Dtos.Survey
{
    public class SurveyQuestionsListDto
    {
        public int SurveyId { get; set; }
        public string SurveyCaption { get; set; }
        public string SurveyDescription { get; set; }
        public SurveyLinguisticType SurveyLinguisticType { get; set; }

        public int? SurveySectionId { get; set; }
        public string SurveySectionCaption { get; set; }
        public int? QuestionId { get; set; }
        public string QuestionText { get; set; }
        public int? QuestionChoiceId { get; set; }
        public string QuestionChoiceText { get; set; }
        public int? AnswerId { get; set; }
        public string AnswersText { get; set; }
        public AnswerType? QuestionType { get; set; }
        public SurveyAnswerState? SurveyAnswerState { get; set; }
        public string QuestionCssClasses { get; set; }

        public bool ShouldQuestionActAsFilter { get; set; }

        public bool IsQuestionActiveForQuestionnaire { get; set; }

        public bool IsQuestionActiveForDashboard { get; set; }
        public QuertionCategory QuestionCategory { get; set; }

        public ChartType QuestionChartType { get; set; }

        public byte QuestionSortOrderForDashboard { get; set; }

        public byte QuestionSortOrderForQuestionnaire { get; set; }
        public int? QuestionParentId { get; set; }
        public byte QuestionChoiceSortOrder { get; set; }
        public bool IsOtherMetaQuestionChoice { get; set; }
    }
}
