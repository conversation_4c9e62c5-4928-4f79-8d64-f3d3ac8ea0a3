﻿using System;

namespace Infodice.WE.Common.DTOs.Teams
{
    public class TeamPositionsDto
    {
        public int TeamId { get; set; }
        public string TeamName { get; set; }
        public string CNIC { get; set; }
        public string Phno { get; set; }
        public string ImageDpUri { get; set; }
        public string PollingStationName { get; set; }
        public string PollinStationCode { get; set; }
        public string PositionTemplateName { get; set; }
        public int PollingStationId { get; set; }
        public int? VoterId { get; set; }
        public int PositionTemplateId { get; set; }
        public string PositionName { get; set; }
        public int PositionId { get; set; }
        public string MemberName { get; set; }
        public WE.Common.Enums.PartyType PositionType { get; set; }
        public int? MemberId { get; set; }
        public int? Id { get; set; }
        public DateTimeOffset? AppointmentDate { get; set; }
    }
}
